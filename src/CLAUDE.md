# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

This is a GPT Engine application built with .NET 8.0 using a layered architecture with the following key components:

- **Mysoft.GPTEngine.WebApplication**: ASP.NET Core Web API application serving as the entry point
- **Mysoft.GPTEngine.Application**: Application layer with business logic and services
- **Mysoft.GPTEngine.Application.Contracts**: Contracts and DTOs for application services
- **Mysoft.GPTEngine.Domain**: Core domain layer with entities, repositories, and domain services
- **Mysoft.GPTEngine.Domain.Shared**: Shared DTOs, constants, interfaces, and utilities
- **Mysoft.GPTEngine.SemanticKernel.Core**: Semantic Kernel integration with various LLM providers (Baidu, Kimi, Mysoft, QianXun, Qwen, Xunfei)
- **Mysoft.GPTEngine.Plugin**: Plugin system for activities and skills
- **Mysoft.GPTEngine.Common**: Common utilities, helpers, and shared infrastructure
- **Mysoft.GPTEngine.Diagnostics.Extend**: Diagnostic and monitoring extensions

## Key Technologies

- **.NET 8.0** with C# 12
- **Semantic Kernel 1.60.0** for AI integration
- **SqlSugar ORM** for database operations
- **Nacos** for configuration management
- **RabbitMQ** for message queuing
- **YARP** for reverse proxy functionality
- **AutoMapper** for object mapping
- **Serilog** for structured logging
- **xUnit** for unit testing
- **Moq** for mocking
- **ModelContextProtocol.AspNetCore** 0.3.0-preview.2 for MCP server functionality

## Development Commands

### Build and Run
```bash
# Build the entire solution
dotnet build

# Run the web application
dotnet run --project Mysoft.GPTEngine.WebApplication

# Run with specific environment
dotnet run --project Mysoft.GPTEngine.WebApplication --launch-profile "Development"
```

### Testing
```bash
# Run all tests
dotnet test

# Run specific test project
dotnet test Mysoft.GPTEngine.Domain.Tests

# Run tests with detailed output
dotnet test --logger "console;verbosity=detailed"

# Run tests with code coverage
dotnet test --collect:"XPlat Code Coverage"
```

### Database Operations
The application uses SqlSugar ORM. Database migrations and schema management are handled through code-first approaches in the Domain layer.

### Configuration
The application uses Nacos for configuration management with the following key configuration sources:
- `global.properties`
- `global_url.properties`
- `tenants.properties`
- `resource_instance.properties`
- `resource_info.properties`

Local configuration can be found in `appsettings.json` and `appsettings.{Environment}.json`.

### MCP Server
The application includes a Model Context Protocol (MCP) server endpoint accessible at:
- Development: `http://localhost:5000/mcp_server/mcp`
- Configurable via Nacos and environment variables
- MCP server provides tool integration capabilities for AI agents

### Development Environment
The application supports multiple development environments:
- **Development**: Local development with IIS Express or Kestrel
- **Test**: Test environment configuration
- **Container**: Docker support with Windows containers

Environment variables are used for configuration:
- `SKYLINE_NACOS_SERVER_ADDRESSES`: Nacos server addresses
- `SKYLINE_NACOS_NAMESPACE_ID`: Nacos namespace
- `SKYLINE_NACOS_GROUP`: Nacos group name
- `SKYLINE_NACOS_USERNAME`: Nacos username
- `SKYLINE_NACOS_PASSWORD`: Nacos password
- `SKYLINE_NACOS_ENCRYPTION_ALGORITHM`: Encryption algorithm (SM4 supported)
- `SKYLINE_NACOS_SECRET`: Encryption secret key
- `LOG_LEVEL`: Logging level
- `GPT_BUILDER_PATH`: GPT Builder service path

## Project Structure

```
src/
├── Mysoft.GPTEngine.WebApplication/        # Web API entry point
│   ├── Controllers/                        # API controllers
│   ├── Middleware/                         # Custom middleware
│   ├── Profiles/                          # Configuration profiles
│   └── LogConfig/                         # Logging configuration
├── Mysoft.GPTEngine.Application/           # Application services
│   ├── ApprovalDatasource/                # Approval data sources
│   ├── Strategies/                        # Execution strategies
│   └── Heartbeat/                        # Heartbeat strategies
├── Mysoft.GPTEngine.Application.Contracts/ # Contracts and DTOs
├── Mysoft.GPTEngine.Domain/               # Domain layer
│   ├── Entity/                           # Domain entities
│   ├── Repositories/                     # Data repositories
│   ├── Services/                         # Domain services
│   ├── AgentSkillEnhancement/            # Agent skill enhancements
│   ├── McpServerEnhancement/             # MCP server enhancements
│   └── ApiAuthorization/                 # API authorization
├── Mysoft.GPTEngine.Domain.Shared/       # Shared contracts
│   ├── Dtos/                             # Data transfer objects
│   ├── Constants/                        # Application constants
│   ├── Enums/                            # Application enums
│   └── Utilities/                        # Utility functions
├── Mysoft.GPTEngine.SemanticKernel.Core/ # AI/LLM integration
│   ├── Baidu/                            # Baidu AI provider
│   ├── Kimi/                             # Kimi AI provider
│   ├── MysoftModel/                      # Mysoft AI provider
│   ├── QianXun/                          # QianXun AI provider
│   ├── Qwen/                             # Qwen AI provider
│   ├── Xunfei/                           # Xunfei AI provider
│   ├── Ocr/                              # OCR services
│   └── ContentReview/                    # Content review services
├── Mysoft.GPTEngine.Plugin/              # Plugin system
│   ├── Activities/                       # Activity implementations
│   ├── System/                           # System plugins
│   └── ContentReview/                    # Content review plugins
├── Mysoft.GPTEngine.Common/              # Common utilities
│   ├── Helper/                           # Helper classes
│   ├── Rabbitmq/                         # RabbitMQ integration
│   ├── Encryption/                      # Encryption utilities
│   └── Chunker/                          # Text chunking utilities
├── Mysoft.GPTEngine.Diagnostics.Extend/  # Monitoring extensions
└── Mysoft.GPTEngine.Domain.Tests/       # Unit tests
```

## Key Features

- **Multi-provider LLM Support**: Supports Baidu, Kimi, Mysoft, QianXun, Qwen, and Xunfei AI providers
- **MCP Server**: Implements Model Context Protocol for AI tool integration
- **Plugin System**: Extensible activity-based plugin architecture
- **Knowledge Management**: Document processing and embedding storage
- **Approval Workflows**: Plan-based approval system with rules
- **API Gateway**: Reverse proxy configuration for external integrations
- **Real-time Communication**: SSE (Server-Sent Events) support
- **Content Review**: Integrated content moderation for multiple providers
- **OCR Services**: Document image recognition and text extraction
- **Agent Skills**: AI agent skill execution and enhancement
- **Document Processing**: Multi-format document processing (PDF, Word, Excel, etc.)
- **Authentication**: Multiple authentication providers (OAuth, IPass, MySoft, NoAuth)

## Key Architecture Patterns

- **Clean Architecture**: Separation of concerns with domain-driven design
- **Repository Pattern**: Data access abstraction with SqlSugar ORM
- **Plugin Architecture**: Extensible plugin system for AI capabilities
- **Event-Driven**: Event bus for decoupled communication
- **Dependency Injection**: IoC container for service management
- **Middleware Pipeline**: ASP.NET Core middleware for request processing
- **Configuration Management**: Nacos-based configuration with environment overrides

## Environment Setup

Required environment variables for Nacos configuration:
- `SKYLINE_NACOS_SERVER_ADDRESSES`
- `SKYLINE_NACOS_NAMESPACE_ID`
- `SKYLINE_NACOS_GROUP`
- `SKYLINE_NACOS_USERNAME`
- `SKYLINE_NACOS_PASSWORD`
- `SKYLINE_NACOS_ENCRYPTION_ALGORITHM`
- `SKYLINE_NACOS_SECRET`

## Docker Support

The application includes Docker support with Windows containers. Dockerfile is located in the WebApplication project.